<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/countries') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Countries
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🌍</span>
            Add New Country
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Create a new country entry in the government structure hierarchy.
        </p>
    </div>

    <!-- Create Country Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">Country Information</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Enter the basic information for the new country.</p>

            <form method="POST" action="<?= base_url('dakoii/government/countries/create') ?>" class="country-form">
                <?= csrf_field() ?>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>

                    <div class="form-group">
                        <label for="name" class="form-label">Country Name *</label>
                        <input type="text" id="name" name="name" class="form-input"
                               value="<?= old('name') ?>" required
                               placeholder="Enter the full country name">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Official name of the country as it should appear in the system
                        </small>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="iso2" class="form-label">ISO2 Code *</label>
                            <input type="text" id="iso2" name="iso2" class="form-input"
                                   value="<?= old('iso2') ?>" required
                                   maxlength="2" style="text-transform: uppercase;"
                                   placeholder="e.g., US, GB, AU">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                2-letter ISO country code
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="iso3" class="form-label">ISO3 Code *</label>
                            <input type="text" id="iso3" name="iso3" class="form-input"
                                   value="<?= old('iso3') ?>" required
                                   maxlength="3" style="text-transform: uppercase;"
                                   placeholder="e.g., USA, GBR, AUS">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                3-letter ISO country code
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Geographic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Geographic Information</h3>

                    <div class="form-group">
                        <label for="geojson_id" class="form-label">GeoJSON ID</label>
                        <input type="text" id="geojson_id" name="geojson_id" class="form-input"
                               value="<?= old('geojson_id') ?>"
                               placeholder="Geographic identifier for mapping">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Optional: Geographic identifier for map integration
                        </small>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="map_centre_gps" class="form-label">Map Center GPS</label>
                            <input type="text" id="map_centre_gps" name="map_centre_gps" class="form-input"
                                   value="<?= old('map_centre_gps') ?>"
                                   placeholder="e.g., -6.314993,143.95555">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Latitude, Longitude coordinates
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="map_zoom" class="form-label">Map Zoom Level</label>
                            <input type="number" id="map_zoom" name="map_zoom" class="form-input"
                                   value="<?= old('map_zoom', '6') ?>" min="1" max="20">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Default zoom level for map display (1-20)
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">💾</i> Create Country
                    </button>
                    <a href="<?= base_url('dakoii/government/countries') ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Help Information -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>Country Creation Guide</h3>
            </div>
            <div class="info-content">
                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">Required Fields</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                        <li style="margin-bottom: var(--spacing-xs);">Country Name: Official name</li>
                        <li style="margin-bottom: var(--spacing-xs);">ISO2: 2-letter country code</li>
                        <li>ISO3: 3-letter country code</li>
                    </ul>
                </div>

                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">ISO Code Examples</h4>
                    <div style="font-family: var(--font-mono); font-size: 0.875rem; color: var(--text-secondary);">
                        <div>Papua New Guinea: PG, PNG</div>
                        <div>Australia: AU, AUS</div>
                        <div>United States: US, USA</div>
                        <div>United Kingdom: GB, GBR</div>
                    </div>
                </div>

                <div style="padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: var(--radius-md);">
                    <div style="display: flex; gap: var(--spacing-sm); align-items: flex-start;">
                        <div style="font-size: 1.25rem;">💡</div>
                        <div>
                            <h4 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 0.9rem;">Tip</h4>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.85rem;">
                                After creating a country, you can add provinces, districts, and LLGs to build the complete government hierarchy.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-uppercase ISO codes
document.getElementById('iso2').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});

document.getElementById('iso3').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});

// Form validation
document.querySelector('.country-form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const iso2 = document.getElementById('iso2').value.trim();
    const iso3 = document.getElementById('iso3').value.trim();

    if (name.length < 2) {
        e.preventDefault();
        alert('Country name must be at least 2 characters long.');
        return false;
    }

    if (iso2.length !== 2) {
        e.preventDefault();
        alert('ISO2 code must be exactly 2 characters.');
        return false;
    }

    if (iso3.length !== 3) {
        e.preventDefault();
        alert('ISO3 code must be exactly 3 characters.');
        return false;
    }
});
</script>
<?= $this->endSection() ?>