<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('content') ?>
<h1>Provinces</h1>
<a class="btn btn-primary" href="<?= base_url('dakoii/government/provinces/create') ?>">Add Province</a>
<table class="table">
    <thead>
        <tr><th>Name</th><th>Province Code</th><th>Country ID</th><th>Actions</th></tr>
    </thead>
    <tbody>
        <?php if (!empty($provinces)): foreach ($provinces as $province): ?>
        <tr>
            <td><?= esc($province['name']) ?></td>
            <td><?= esc($province['prov_code']) ?></td>
            <td><?= esc($province['country_id']) ?></td>
            <td>
                <a href="<?= base_url('dakoii/government/provinces/'.$province['id']) ?>">View</a> |
                <a href="<?= base_url('dakoii/government/provinces/'.$province['id'].'/edit') ?>">Edit</a> |
                <form action="<?= base_url('dakoii/government/provinces/'.$province['id'].'/delete') ?>" method="post" style="display:inline;">
                    <?= csrf_field() ?>
                    <button type="submit" onclick="return confirm('Delete this province?')">Delete</button>
                </form>
            </td>
        </tr>
        <?php endforeach; else: ?>
        <tr><td colspan="4">No provinces found.</td></tr>
        <?php endif; ?>
    </tbody>
</table>
<?= $this->endSection() ?> 