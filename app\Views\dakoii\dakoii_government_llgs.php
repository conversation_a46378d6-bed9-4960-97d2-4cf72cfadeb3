<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('content') ?>
<h1>LLGs</h1>
<a class="btn btn-primary" href="<?= base_url('dakoii/government/llgs/create') ?>">Add LLG</a>
<table class="table">
    <thead>
        <tr><th>Name</th><th>LLG Code</th><th>District ID</th><th>Actions</th></tr>
    </thead>
    <tbody>
        <?php if (!empty($llgs)): foreach ($llgs as $llg): ?>
        <tr>
            <td><?= esc($llg['name']) ?></td>
            <td><?= esc($llg['llg_code']) ?></td>
            <td><?= esc($llg['district_id']) ?></td>
            <td>
                <a href="<?= base_url('dakoii/government/llgs/'.$llg['id']) ?>">View</a> |
                <a href="<?= base_url('dakoii/government/llgs/'.$llg['id'].'/edit') ?>">Edit</a> |
                <form action="<?= base_url('dakoii/government/llgs/'.$llg['id'].'/delete') ?>" method="post" style="display:inline;">
                    <?= csrf_field() ?>
                    <button type="submit" onclick="return confirm('Delete this LLG?')">Delete</button>
                </form>
            </td>
        </tr>
        <?php endforeach; else: ?>
        <tr><td colspan="4">No LLGs found.</td></tr>
        <?php endif; ?>
    </tbody>
</table>
<?= $this->endSection() ?> 