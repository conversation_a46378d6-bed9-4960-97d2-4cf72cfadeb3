<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('sidebar') ?>
<div class="nav-item">
    <a href="<?= base_url('dakoii/dashboard') ?>" class="nav-link">
        <span class="nav-icon">📊</span>
        <span class="nav-text">Dashboard</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/organizations') ?>" class="nav-link">
        <span class="nav-icon">🏢</span>
        <span class="nav-text">Organizations</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/government') ?>" class="nav-link active">
        <span class="nav-icon">🏛️</span>
        <span class="nav-text">Government Structure</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/users') ?>" class="nav-link">
        <span class="nav-icon">👥</span>
        <span class="nav-text">Users</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/reports') ?>" class="nav-link">
        <span class="nav-icon">📈</span>
        <span class="nav-text">Reports</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/settings') ?>" class="nav-link">
        <span class="nav-icon">⚙️</span>
        <span class="nav-text">Settings</span>
    </a>
</div>
<div class="nav-item" style="margin-top: auto;">
    <a href="<?= base_url('dakoii/logout') ?>" class="nav-link">
        <span class="nav-icon">🚪</span>
        <span class="nav-text">Logout</span>
    </a>
</div>
<?= $this->endSection() ?>
<?= $this->section('content') ?>
<h1>Country Profile</h1>
<?php if (isset($country)): ?>
    <ul>
        <li><strong>Name:</strong> <?= esc($country['name']) ?></li>
        <li><strong>ISO2:</strong> <?= esc($country['iso2']) ?></li>
        <li><strong>ISO3:</strong> <?= esc($country['iso3']) ?></li>
    </ul>
    <a class="btn btn-secondary" href="<?= base_url('dakoii/government/countries/'.$country['id'].'/edit') ?>">Edit</a>
    <form action="<?= base_url('dakoii/government/countries/'.$country['id'].'/delete') ?>" method="post" style="display:inline;">
        <?= csrf_field() ?>
        <button type="submit" onclick="return confirm('Delete this country?')">Delete</button>
    </form>
<?php else: ?>
    <p>Country not found.</p>
<?php endif; ?>
<?= $this->endSection() ?> 