<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('content') ?>
<h1>Countries</h1>
<a class="btn btn-primary" href="<?= base_url('dakoii/government/countries/create') ?>">Add Country</a>
<table class="table">
    <thead>
        <tr><th>Name</th><th>ISO2</th><th>ISO3</th><th>Actions</th></tr>
    </thead>
    <tbody>
        <?php if (!empty($countries)): foreach ($countries as $country): ?>
        <tr>
            <td><?= esc($country['name']) ?></td>
            <td><?= esc($country['iso2']) ?></td>
            <td><?= esc($country['iso3']) ?></td>
            <td>
                <a href="<?= base_url('dakoii/government/countries/'.$country['id']) ?>">View</a> |
                <a href="<?= base_url('dakoii/government/countries/'.$country['id'].'/edit') ?>">Edit</a> |
                <form action="<?= base_url('dakoii/government/countries/'.$country['id'].'/delete') ?>" method="post" style="display:inline;">
                    <?= csrf_field() ?>
                    <button type="submit" onclick="return confirm('Delete this country?')">Delete</button>
                </form>
            </td>
        </tr>
        <?php endforeach; else: ?>
        <tr><td colspan="4">No countries found.</td></tr>
        <?php endif; ?>
    </tbody>
</table>
<?= $this->endSection() ?> 