<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('content') ?>
<h1>Districts</h1>
<a class="btn btn-primary" href="<?= base_url('dakoii/government/districts/create') ?>">Add District</a>
<table class="table">
    <thead>
        <tr><th>Name</th><th>District Code</th><th>Province ID</th><th>Actions</th></tr>
    </thead>
    <tbody>
        <?php if (!empty($districts)): foreach ($districts as $district): ?>
        <tr>
            <td><?= esc($district['name']) ?></td>
            <td><?= esc($district['dist_code']) ?></td>
            <td><?= esc($district['province_id']) ?></td>
            <td>
                <a href="<?= base_url('dakoii/government/districts/'.$district['id']) ?>">View</a> |
                <a href="<?= base_url('dakoii/government/districts/'.$district['id'].'/edit') ?>">Edit</a> |
                <form action="<?= base_url('dakoii/government/districts/'.$district['id'].'/delete') ?>" method="post" style="display:inline;">
                    <?= csrf_field() ?>
                    <button type="submit" onclick="return confirm('Delete this district?')">Delete</button>
                </form>
            </td>
        </tr>
        <?php endforeach; else: ?>
        <tr><td colspan="4">No districts found.</td></tr>
        <?php endif; ?>
    </tbody>
</table>
<?= $this->endSection() ?> 