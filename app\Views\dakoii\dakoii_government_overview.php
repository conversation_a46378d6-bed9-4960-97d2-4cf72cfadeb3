<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/chart') ?>" class="btn btn-secondary">
    <i class="icon">📊</i> Hierarchy Chart
</a>
<a href="<?= base_url('dakoii/government/map') ?>" class="btn btn-primary">
    <i class="icon">🗺️</i> Geographic Map
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Government Structure Overview -->
    <div style="margin-bottom: var(--spacing-2xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-md); font-size: 2rem;">Government Structure Management</h1>
        <p style="color: var(--text-secondary); font-size: 1.1rem; margin-bottom: var(--spacing-xl);">
            Manage the hierarchical government structure from countries down to Local Level Governments (LLGs).
        </p>
    </div>

    <!-- Government Levels Grid -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-xl); margin-bottom: var(--spacing-2xl);">
        <!-- Countries Card -->
        <div class="card">
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                <div style="font-size: 3rem;">🌍</div>
                <div>
                    <h3 style="margin: 0; color: var(--text-primary); font-size: 1.5rem;">Countries</h3>
                    <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">Top-level administrative divisions</p>
                </div>
            </div>
            <div style="margin-bottom: var(--spacing-lg);">
                <div style="font-size: 2rem; font-weight: 700; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= isset($stats['countries']) ? number_format($stats['countries']) : '0' ?>
                </div>
                <div style="color: var(--text-tertiary); font-size: 0.875rem;">Total Countries</div>
            </div>
            <div style="display: flex; gap: var(--spacing-sm);">
                <a href="<?= base_url('dakoii/government/countries') ?>" class="btn btn-primary" style="flex: 1; text-align: center;">
                    👁️ View All
                </a>
                <a href="<?= base_url('dakoii/government/countries/create') ?>" class="btn btn-secondary">
                    ➕
                </a>
            </div>
        </div>

        <!-- Provinces Card -->
        <div class="card">
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                <div style="font-size: 3rem;">🏞️</div>
                <div>
                    <h3 style="margin: 0; color: var(--text-primary); font-size: 1.5rem;">Provinces</h3>
                    <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">Provincial administrative regions</p>
                </div>
            </div>
            <div style="margin-bottom: var(--spacing-lg);">
                <div style="font-size: 2rem; font-weight: 700; background: var(--gradient-secondary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= isset($stats['provinces']) ? number_format($stats['provinces']) : '0' ?>
                </div>
                <div style="color: var(--text-tertiary); font-size: 0.875rem;">Total Provinces</div>
            </div>
            <div style="display: flex; gap: var(--spacing-sm);">
                <a href="<?= base_url('dakoii/government/provinces') ?>" class="btn btn-primary" style="flex: 1; text-align: center;">
                    👁️ View All
                </a>
                <a href="<?= base_url('dakoii/government/provinces/create') ?>" class="btn btn-secondary">
                    ➕
                </a>
            </div>
        </div>

        <!-- Districts Card -->
        <div class="card">
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                <div style="font-size: 3rem;">🏘️</div>
                <div>
                    <h3 style="margin: 0; color: var(--text-primary); font-size: 1.5rem;">Districts</h3>
                    <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">District administrative areas</p>
                </div>
            </div>
            <div style="margin-bottom: var(--spacing-lg);">
                <div style="font-size: 2rem; font-weight: 700; background: var(--gradient-accent); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= isset($stats['districts']) ? number_format($stats['districts']) : '0' ?>
                </div>
                <div style="color: var(--text-tertiary); font-size: 0.875rem;">Total Districts</div>
            </div>
            <div style="display: flex; gap: var(--spacing-sm);">
                <a href="<?= base_url('dakoii/government/districts') ?>" class="btn btn-primary" style="flex: 1; text-align: center;">
                    👁️ View All
                </a>
                <a href="<?= base_url('dakoii/government/districts/create') ?>" class="btn btn-secondary">
                    ➕
                </a>
            </div>
        </div>

        <!-- LLGs Card -->
        <div class="card">
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                <div style="font-size: 3rem;">🏛️</div>
                <div>
                    <h3 style="margin: 0; color: var(--text-primary); font-size: 1.5rem;">Local Level Governments</h3>
                    <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">Lowest administrative level</p>
                </div>
            </div>
            <div style="margin-bottom: var(--spacing-lg);">
                <div style="font-size: 2rem; font-weight: 700; color: var(--text-primary);">
                    <?= isset($stats['llgs']) ? number_format($stats['llgs']) : '0' ?>
                </div>
                <div style="color: var(--text-tertiary); font-size: 0.875rem;">Total LLGs</div>
            </div>
            <div style="display: flex; gap: var(--spacing-sm);">
                <a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-primary" style="flex: 1; text-align: center;">
                    👁️ View All
                </a>
                <a href="<?= base_url('dakoii/government/llgs/create') ?>" class="btn btn-secondary">
                    ➕
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
        <div class="card-header">Quick Actions</div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-md);">
            <a href="<?= base_url('dakoii/government/chart') ?>" class="btn btn-secondary" style="padding: var(--spacing-lg); text-align: center; display: flex; flex-direction: column; align-items: center; gap: var(--spacing-sm);">
                <div style="font-size: 2rem;">📊</div>
                <div>View Hierarchy Chart</div>
                <small style="color: var(--text-tertiary);">Interactive government structure visualization</small>
            </a>
            <a href="<?= base_url('dakoii/government/map') ?>" class="btn btn-secondary" style="padding: var(--spacing-lg); text-align: center; display: flex; flex-direction: column; align-items: center; gap: var(--spacing-sm);">
                <div style="font-size: 2rem;">🗺️</div>
                <div>Geographic Map</div>
                <small style="color: var(--text-tertiary);">View government units on map</small>
            </a>
            <a href="<?= base_url('dakoii/government/import') ?>" class="btn btn-secondary" style="padding: var(--spacing-lg); text-align: center; display: flex; flex-direction: column; align-items: center; gap: var(--spacing-sm);">
                <div style="font-size: 2rem;">📥</div>
                <div>Import Data</div>
                <small style="color: var(--text-tertiary);">Bulk import government structure data</small>
            </a>
            <a href="<?= base_url('dakoii/government/export') ?>" class="btn btn-secondary" style="padding: var(--spacing-lg); text-align: center; display: flex; flex-direction: column; align-items: center; gap: var(--spacing-sm);">
                <div style="font-size: 2rem;">📤</div>
                <div>Export Data</div>
                <small style="color: var(--text-tertiary);">Export government structure to CSV/Excel</small>
            </a>
        </div>
    </div>
</div>
<?= $this->endSection() ?>