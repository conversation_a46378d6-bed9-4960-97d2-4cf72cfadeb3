<?= $this->extend('templates/dakoii_portal_template') ?>
<?= $this->section('sidebar') ?>
<div class="nav-item">
    <a href="<?= base_url('dakoii/dashboard') ?>" class="nav-link">
        <span class="nav-icon">📊</span>
        <span class="nav-text">Dashboard</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/organizations') ?>" class="nav-link">
        <span class="nav-icon">🏢</span>
        <span class="nav-text">Organizations</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/government') ?>" class="nav-link active">
        <span class="nav-icon">🏛️</span>
        <span class="nav-text">Government Structure</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/users') ?>" class="nav-link">
        <span class="nav-icon">👥</span>
        <span class="nav-text">Users</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/reports') ?>" class="nav-link">
        <span class="nav-icon">📈</span>
        <span class="nav-text">Reports</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/settings') ?>" class="nav-link">
        <span class="nav-icon">⚙️</span>
        <span class="nav-text">Settings</span>
    </a>
</div>
<div class="nav-item" style="margin-top: auto;">
    <a href="<?= base_url('dakoii/logout') ?>" class="nav-link">
        <span class="nav-icon">🚪</span>
        <span class="nav-text">Logout</span>
    </a>
</div>
<?= $this->endSection() ?>
<?= $this->section('content') ?>
<h1>Government Structure Overview</h1>
<ul>
    <li><a href="<?= base_url('dakoii/government/countries') ?>">Countries</a></li>
    <li><a href="<?= base_url('dakoii/government/provinces') ?>">Provinces</a></li>
    <li><a href="<?= base_url('dakoii/government/districts') ?>">Districts</a></li>
    <li><a href="<?= base_url('dakoii/government/llgs') ?>">LLGs</a></li>
    <li><a href="<?= base_url('dakoii/government/chart') ?>">Hierarchy Chart</a></li>
    <li><a href="<?= base_url('dakoii/government/map') ?>">Geographic Map</a></li>
</ul>
<?= $this->endSection() ?> 