<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/districts') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Districts
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🏘️</span>
            Add New District
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Create a new district entry in the government structure hierarchy.
        </p>
    </div>

    <!-- Create District Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">District Information</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Enter the basic information for the new district.</p>

            <form method="POST" action="<?= base_url('dakoii/government/districts/create') ?>" class="district-form">
                <?= csrf_field() ?>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>

                    <div class="form-group">
                        <label for="province_id" class="form-label">Province *</label>
                        <select id="province_id" name="province_id" class="form-input" required>
                            <option value="">Select Province</option>
                            <?php if (isset($provinces) && !empty($provinces)): ?>
                                <?php foreach ($provinces as $province): ?>
                                    <option value="<?= $province['id'] ?>" <?= (old('province_id') == $province['id']) ? 'selected' : '' ?>>
                                        <?= esc($province['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Select the province this district belongs to
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="name" class="form-label">District Name *</label>
                        <input type="text" id="name" name="name" class="form-input"
                               value="<?= old('name') ?>" required
                               placeholder="Enter the full district name">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Official name of the district as it should appear in the system
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="dist_code" class="form-label">District Code *</label>
                        <input type="text" id="dist_code" name="dist_code" class="form-input"
                               value="<?= old('dist_code') ?>" required
                               placeholder="e.g., SFD, PMD, LAE"
                               style="text-transform: uppercase;">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Short code or abbreviation for the district
                        </small>
                    </div>
                </div>

                <!-- Geographic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Geographic Information</h3>

                    <div class="form-group">
                        <label for="geojson_id" class="form-label">GeoJSON ID</label>
                        <select id="geojson_id" name="geojson_id" class="form-input">
                            <option value="">Select District Boundary (Optional)</option>
                            <?php
                            // Load district options from JSON
                            $jsonPath = FCPATH . 'map_jsons/png_dist_boundaries_2011.json';
                            if (file_exists($jsonPath)) {
                                $jsonData = json_decode(file_get_contents($jsonPath), true);
                                if (isset($jsonData['features'])) {
                                    $districts = [];
                                    foreach ($jsonData['features'] as $feature) {
                                        if (isset($feature['properties']['GEOCODE']) && isset($feature['properties']['DISTNAME'])) {
                                            $districts[] = [
                                                'id' => $feature['properties']['GEOCODE'],
                                                'name' => $feature['properties']['DISTNAME']
                                            ];
                                        }
                                    }
                                    // Sort by name
                                    usort($districts, function($a, $b) {
                                        return strcmp($a['name'], $b['name']);
                                    });

                                    foreach ($districts as $district) {
                                        $selected = (old('geojson_id') == $district['id']) ? 'selected' : '';
                                        echo '<option value="' . esc($district['id']) . '" ' . $selected . '>' . esc($district['name']) . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Optional: Select the corresponding boundary from map data
                        </small>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="map_centre_gps" class="form-label">Map Center GPS</label>
                            <input type="text" id="map_centre_gps" name="map_centre_gps" class="form-input"
                                   value="<?= old('map_centre_gps') ?>"
                                   placeholder="e.g., -6.314993,143.95555">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Latitude, Longitude coordinates
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="map_zoom" class="form-label">Map Zoom Level</label>
                            <input type="number" id="map_zoom" name="map_zoom" class="form-input"
                                   value="<?= old('map_zoom', '10') ?>" min="1" max="20">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Default zoom level for map display (1-20)
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">💾</i> Create District
                    </button>
                    <a href="<?= base_url('dakoii/government/districts') ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Help Information -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>District Creation Guide</h3>
            </div>
            <div class="info-content">
                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">Required Fields</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                        <li style="margin-bottom: var(--spacing-xs);">Province: Parent province</li>
                        <li style="margin-bottom: var(--spacing-xs);">District Name: Official name</li>
                        <li>District Code: Short abbreviation</li>
                    </ul>
                </div>

                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">District Code Examples</h4>
                    <div style="font-family: var(--font-mono); font-size: 0.875rem; color: var(--text-secondary);">
                        <div>South Fly District: SFD</div>
                        <div>Port Moresby District: PMD</div>
                        <div>Lae District: LAE</div>
                        <div>Mount Hagen District: MHD</div>
                    </div>
                </div>

                <div style="padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: var(--radius-md);">
                    <div style="display: flex; gap: var(--spacing-sm); align-items: flex-start;">
                        <div style="font-size: 1.25rem;">💡</div>
                        <div>
                            <h4 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 0.9rem;">Tip</h4>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.85rem;">
                                After creating a district, you can add Local Level Governments (LLGs) to complete the administrative hierarchy.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-uppercase district code
document.getElementById('dist_code').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});

// Form validation
document.querySelector('.district-form').addEventListener('submit', function(e) {
    const provinceId = document.getElementById('province_id').value;
    const name = document.getElementById('name').value.trim();
    const distCode = document.getElementById('dist_code').value.trim();

    if (!provinceId) {
        e.preventDefault();
        alert('Please select a province.');
        return false;
    }

    if (name.length < 2) {
        e.preventDefault();
        alert('District name must be at least 2 characters long.');
        return false;
    }

    if (distCode.length < 2) {
        e.preventDefault();
        alert('District code must be at least 2 characters long.');
        return false;
    }
});
</script>
<?= $this->endSection() ?>