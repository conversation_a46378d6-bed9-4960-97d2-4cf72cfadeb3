<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<div class="nav-item">
    <a href="<?= base_url('dakoii/dashboard') ?>" class="nav-link active">
        <span class="nav-icon">📊</span>
        <span class="nav-text">Dashboard</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/organizations') ?>" class="nav-link">
        <span class="nav-icon">🏢</span>
        <span class="nav-text">Organizations</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/government') ?>" class="nav-link">
        <span class="nav-icon">🏛️</span>
        <span class="nav-text">Government Structure</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/users') ?>" class="nav-link">
        <span class="nav-icon">👥</span>
        <span class="nav-text">Users</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/reports') ?>" class="nav-link">
        <span class="nav-icon">📈</span>
        <span class="nav-text">Reports</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/settings') ?>" class="nav-link">
        <span class="nav-icon">⚙️</span>
        <span class="nav-text">Settings</span>
    </a>
</div>
<div class="nav-item" style="margin-top: auto;">
    <a href="<?= base_url('dakoii/logout') ?>" class="nav-link">
        <span class="nav-icon">🚪</span>
        <span class="nav-text">Logout</span>
    </a>
</div>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<button class="btn btn-secondary" onclick="exportData('csv')">
    Export CSV
</button>
<button class="btn btn-primary" onclick="refreshDashboard()">
    Refresh
</button>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Quick Stats Cards -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: var(--spacing-xl); margin-bottom: var(--spacing-2xl);">
        <!-- Organizations Card -->
        <div class="card">
            <div class="card-header">Organizations</div>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-lg);">
                <div style="font-size: 2.5rem; font-weight: 700; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= number_format($stats['organizations']['total']) ?>
                </div>
                <div style="text-align: right;">
                    <div style="font-size: 0.875rem; color: var(--text-tertiary);">Growth</div>
                    <div style="font-size: 1.25rem; font-weight: 600; color: <?= $stats['organizations']['growth_percentage'] >= 0 ? '#06FFA5' : '#FF006E' ?>;">
                        <?= $stats['organizations']['growth_percentage'] >= 0 ? '+' : '' ?><?= $stats['organizations']['growth_percentage'] ?>%
                    </div>
                </div>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                <div>
                    <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em;">Active</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #06FFA5;"><?= $stats['organizations']['active'] ?></div>
                </div>
                <div>
                    <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em;">Inactive</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--text-muted);"><?= $stats['organizations']['inactive'] ?></div>
                </div>
            </div>
        </div>

        <!-- Dakoii Users Card -->
        <div class="card">
            <div class="card-header">Dakoii Users</div>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-lg);">
                <div style="font-size: 2.5rem; font-weight: 700; background: var(--gradient-secondary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= number_format($stats['dakoii_users']['total']) ?>
                </div>
                <div style="text-align: right;">
                    <div style="font-size: 0.875rem; color: var(--text-tertiary);">Growth</div>
                    <div style="font-size: 1.25rem; font-weight: 600; color: <?= $stats['dakoii_users']['growth_percentage'] >= 0 ? '#06FFA5' : '#FF006E' ?>;">
                        <?= $stats['dakoii_users']['growth_percentage'] >= 0 ? '+' : '' ?><?= $stats['dakoii_users']['growth_percentage'] ?>%
                    </div>
                </div>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                <div>
                    <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em;">Activated</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #06FFA5;"><?= $stats['dakoii_users']['activated'] ?></div>
                </div>
                <div>
                    <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em;">Pending</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #FFB700;"><?= $stats['dakoii_users']['pending'] ?></div>
                </div>
            </div>
        </div>

        <!-- License Revenue Card -->
        <div class="card">
            <div class="card-header">License Status</div>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-lg);">
                <div style="font-size: 2.5rem; font-weight: 700; background: var(--gradient-accent); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= $stats['license_revenue']['paid_percentage'] ?>%
                </div>
                <div style="text-align: right;">
                    <div style="font-size: 0.875rem; color: var(--text-tertiary);">Paid Rate</div>
                </div>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                <div>
                    <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em;">Paid</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #06FFA5;"><?= $stats['organizations']['paid_licenses'] ?></div>
                </div>
                <div>
                    <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em;">Unpaid</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #FF006E;"><?= $stats['organizations']['unpaid_licenses'] ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-2xl);">
        <!-- Organizations Growth Chart -->
        <div class="card">
            <div class="card-header">Organizations Growth (Last 12 Months)</div>
            <div style="height: 300px; position: relative;">
                <canvas id="organizationsChart"></canvas>
            </div>
        </div>

        <!-- License Status Pie Chart -->
        <div class="card">
            <div class="card-header">License Distribution</div>
            <div style="height: 300px; position: relative; display: flex; align-items: center; justify-content: center;">
                <canvas id="licenseChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="card">
        <div class="card-header">Recent Activity</div>
        <div id="recentActivity">
            <?php if (empty($recent_activity)): ?>
                <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-tertiary);">
                    <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📊</div>
                    <div>No recent activity to display</div>
                </div>
            <?php else: ?>
                <?php foreach ($recent_activity as $activity): ?>
                    <div style="display: flex; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--glass-bg); display: flex; align-items: center; justify-content: center; margin-right: var(--spacing-md);">
                            <?= $activity['icon'] === 'building' ? '🏢' : '👤' ?>
                        </div>
                        <div style="flex: 1;">
                            <div style="color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                <?= esc($activity['message']) ?>
                            </div>
                            <div style="font-size: 0.75rem; color: var(--text-tertiary);">
                                <?= date('M j, Y \a\t g:i A', strtotime($activity['timestamp'])) ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Organizations Growth Chart
    fetch('<?= base_url('dakoii/dashboard/chart-data?type=organizations_monthly') ?>')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('organizationsChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#B8BCC8'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#7C8091'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.05)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#7C8091'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.05)'
                            }
                        }
                    }
                }
            });
        });

    // License Status Chart
    fetch('<?= base_url('dakoii/dashboard/chart-data?type=license_status') ?>')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('licenseChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#B8BCC8',
                                padding: 20
                            }
                        }
                    }
                }
            });
        });
}

function refreshDashboard() {
    location.reload();
}

function exportData(format) {
    window.open('<?= base_url('dakoii/dashboard/export?format=') ?>' + format, '_blank');
}
</script>
<?= $this->endSection() ?>
