<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<div class="nav-item">
    <a href="<?= base_url('dakoii/dashboard') ?>" class="nav-link">
        <span class="nav-icon">📊</span>
        <span class="nav-text">Dashboard</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/organizations') ?>" class="nav-link">
        <span class="nav-icon">🏢</span>
        <span class="nav-text">Organizations</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/users') ?>" class="nav-link active">
        <span class="nav-icon">👥</span>
        <span class="nav-text">System Users</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/government') ?>" class="nav-link">
        <span class="nav-icon">🏛️</span>
        <span class="nav-text">Government Structure</span>
    </a>
</div>
<div class="nav-item" style="margin-top: auto;">
    <a href="<?= base_url('dakoii/logout') ?>" class="nav-link">
        <span class="nav-icon">🚪</span>
        <span class="nav-text">Logout</span>
    </a>
</div>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Users
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); color: #28a745; padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
            <?= session()->getFlashdata('success') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #dc3545; padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #dc3545; padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
            <ul style="margin: 0; padding-left: var(--spacing-md);">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Create User Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">Create New System User</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Create a new administrator account for the Dakoii Portal system.</p>

            <form method="POST" action="<?= base_url('dakoii/users/create') ?>">
                <?= csrf_field() ?>

                <!-- Step 1: Basic Information -->
                <div style="margin-bottom: var(--spacing-xl); padding-bottom: var(--spacing-lg); border-bottom: 1px solid var(--glass-border);">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                        <div class="form-group">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-input"
                                   value="<?= old('name') ?>" required>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">Enter the user's full display name</small>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-input"
                                   value="<?= old('email') ?>" required>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">This will be used for login and notifications</small>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" id="username" name="username" class="form-input"
                                   value="<?= old('username') ?>" required>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">Unique username for login (3-50 characters)</small>
                        </div>

                        <div class="form-group">
                            <label for="role" class="form-label">System Role *</label>
                            <select id="role" name="role" class="form-input" required>
                                <option value="">Select Role</option>
                                <?php if ($current_user['role'] === 'admin'): ?>
                                    <option value="admin" <?= old('role') === 'admin' ? 'selected' : '' ?>>Administrator</option>
                                <?php endif; ?>
                                <option value="moderator" <?= old('role') === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                                <option value="user" <?= old('role') === 'user' ? 'selected' : '' ?>>User</option>
                            </select>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary); line-height: 1.4;">
                                <strong>Admin:</strong> Full system access<br>
                                <strong>Moderator:</strong> Can manage users and organizations<br>
                                <strong>User:</strong> Read-only access with limited permissions
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Security Settings -->
                <div style="margin-bottom: var(--spacing-xl);">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Security Settings</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                        <div class="form-group">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" id="password" name="password" class="form-input" required>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">Minimum 8 characters, mix of letters, numbers, and symbols recommended</small>
                        </div>

                        <div class="form-group">
                            <label for="password_confirm" class="form-label">Confirm Password *</label>
                            <input type="password" id="password_confirm" name="password_confirm" class="form-input" required>
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">Re-enter the password to confirm</small>
                        </div>
                    </div>

                    <div style="display: flex; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: var(--radius-md);">
                        <div style="font-size: 1.5rem; flex-shrink: 0;">🔒</div>
                        <div>
                            <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary);">Security Notice</h4>
                            <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                                <li style="margin-bottom: var(--spacing-xs);">The user will receive an activation email before they can log in</li>
                                <li style="margin-bottom: var(--spacing-xs);">Passwords are encrypted using Argon2ID hashing</li>
                                <li style="margin-bottom: var(--spacing-xs);">Users can change their password after first login</li>
                                <li>Account activity is logged for security auditing</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--glass-border);">
                    <button type="submit" class="btn btn-primary">
                        👤 Create User
                    </button>
                    <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
                        ✖️ Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- User Code Preview -->
        <div class="card">
            <div class="card-header">User Code Generation</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-md);">A unique user code will be automatically generated for this user:</p>
            <ul style="margin: 0 0 var(--spacing-md) 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                <li style="margin-bottom: var(--spacing-xs);">10-12 alphanumeric characters</li>
                <li style="margin-bottom: var(--spacing-xs);">Globally unique across all system users</li>
                <li style="margin-bottom: var(--spacing-xs);">Used for internal identification and auditing</li>
                <li>Cannot be changed after creation</li>
            </ul>
            <div style="padding: var(--spacing-md); background: rgba(0, 0, 0, 0.2); border-radius: var(--radius-md); border: 1px solid var(--glass-border);">
                <span style="color: var(--text-secondary); font-size: 0.9rem;">Example:</span>
                <div style="font-family: var(--font-mono); font-size: 1.1rem; font-weight: bold; color: #06FFA5; margin-top: var(--spacing-xs);">ABC123XYZ789</div>
            </div>
        </div>
    </div>
</div>

<script>
// Password strength checker
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);

    // Remove existing strength indicator
    const existingIndicator = this.parentNode.querySelector('.password-strength');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Add new strength indicator
    if (password.length > 0) {
        const indicator = document.createElement('div');
        indicator.style.cssText = 'margin-top: var(--spacing-xs); height: 4px; background: rgba(255, 255, 255, 0.1); border-radius: 2px; overflow: hidden;';

        const bar = document.createElement('div');
        bar.style.cssText = `height: 100%; transition: width 0.3s ease, background-color 0.3s ease; width: ${strength.percentage}%; background: ${getStrengthColor(strength.level)};`;

        indicator.appendChild(bar);
        this.parentNode.appendChild(indicator);
    }
});

function calculatePasswordStrength(password) {
    let score = 0;

    if (password.length >= 8) score += 25;
    if (password.length >= 12) score += 25;
    if (/[a-z]/.test(password)) score += 10;
    if (/[A-Z]/.test(password)) score += 10;
    if (/[0-9]/.test(password)) score += 10;
    if (/[^A-Za-z0-9]/.test(password)) score += 20;

    let level = 'weak';
    if (score >= 70) level = 'strong';
    else if (score >= 50) level = 'good';
    else if (score >= 30) level = 'fair';

    return { percentage: Math.min(score, 100), level: level };
}

function getStrengthColor(level) {
    switch(level) {
        case 'weak': return '#dc3545';
        case 'fair': return '#fd7e14';
        case 'good': return '#ffc107';
        case 'strong': return '#28a745';
        default: return '#dc3545';
    }
}

// Email validation
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (emailRegex.test(email)) {
        this.style.borderColor = '#28a745';
    } else if (email.length > 0) {
        this.style.borderColor = '#dc3545';
    }
});

// Password confirmation
document.getElementById('password_confirm').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirm = this.value;

    if (confirm.length > 0) {
        if (password === confirm) {
            this.style.borderColor = '#28a745';
        } else {
            this.style.borderColor = '#dc3545';
        }
    }
});

// Form validation before submit
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirm = document.getElementById('password_confirm').value;

    if (password !== confirm) {
        e.preventDefault();
        alert('Passwords do not match. Please check and try again.');
        return false;
    }

    if (password.length < 8) {
        e.preventDefault();
        alert('Password must be at least 8 characters long.');
        return false;
    }
});
</script>
<?= $this->endSection() ?>
