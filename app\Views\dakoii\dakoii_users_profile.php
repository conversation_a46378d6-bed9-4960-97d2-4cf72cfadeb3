<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('sidebar') ?>
<div class="nav-item">
    <a href="<?= base_url('dakoii/dashboard') ?>" class="nav-link">
        <span class="nav-icon">📊</span>
        <span class="nav-text">Dashboard</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/organizations') ?>" class="nav-link">
        <span class="nav-icon">🏢</span>
        <span class="nav-text">Organizations</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/users') ?>" class="nav-link active">
        <span class="nav-icon">👥</span>
        <span class="nav-text">System Users</span>
    </a>
</div>
<div class="nav-item">
    <a href="<?= base_url('dakoii/government') ?>" class="nav-link">
        <span class="nav-icon">🏛️</span>
        <span class="nav-text">Government Structure</span>
    </a>
</div>
<div class="nav-item" style="margin-top: auto;">
    <a href="<?= base_url('dakoii/logout') ?>" class="nav-link">
        <span class="nav-icon">🚪</span>
        <span class="nav-text">Logout</span>
    </a>
</div>
<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Users
</a>

<?php 
$canEdit = false;
if ($current_user['role'] === 'admin') {
    $canEdit = true;
} elseif ($current_user['role'] === 'moderator' && $user['role'] !== 'admin') {
    $canEdit = true;
} elseif ($current_user['id'] == $user['id']) {
    $canEdit = true;
}
?>

<?php if ($canEdit): ?>
<a href="<?= base_url('dakoii/users/' . $user['id'] . '/edit') ?>" class="btn btn-primary">
    <i class="icon">✏️</i> Edit Profile
</a>
<?php endif; ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); color: #28a745; padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
            <?= session()->getFlashdata('success') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #dc3545; padding: var(--spacing-md); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <!-- User Profile -->
    <div style="max-width: 1200px; margin: 0 auto;">
        <!-- Profile Header -->
        <div class="card" style="margin-bottom: var(--spacing-xl);"">
            <div style="display: flex; align-items: center; gap: var(--spacing-lg);">
                <div style="width: 120px; height: 120px; border-radius: 50%; overflow: hidden; flex-shrink: 0; border: 4px solid #06FFA5;">
                    <?php if (!empty($user['id_photo_path']) && file_exists(ROOTPATH . $user['id_photo_path'])): ?>
                        <img src="<?= base_url($user['id_photo_path']) ?>" alt="<?= esc($user['name']) ?>" style="width: 100%; height: 100%; object-fit: cover;">
                    <?php else: ?>
                        <div style="width: 100%; height: 100%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 3rem; color: white;">
                            <?= strtoupper(substr($user['name'], 0, 1)) ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div style="flex: 1;">
                    <h1 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 2rem;"><?= esc($user['name']) ?></h1>
                    <p style="margin: 0 0 var(--spacing-xs) 0;">
                        <span style="padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.8rem; font-weight: 500; text-transform: uppercase;
                            <?php if ($user['role'] === 'admin'): ?>background: #dc3545; color: white;
                            <?php elseif ($user['role'] === 'moderator'): ?>background: #fd7e14; color: white;
                            <?php else: ?>background: #6c757d; color: white;<?php endif; ?>">
                            <?= ucfirst($user['role']) ?>
                        </span>
                    </p>
                    <p style="margin: var(--spacing-xs) 0; color: var(--text-secondary);">User Code: <?= esc($user['user_code']) ?></p>
                    <p style="margin: var(--spacing-xs) 0; color: var(--text-secondary);">
                        Status:
                        <span style="padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.8rem; font-weight: 500; text-transform: uppercase;
                            <?= $user['is_activated'] ? 'background: #28a745; color: white;' : 'background: #6c757d; color: white;' ?>">
                            <?= $user['is_activated'] ? 'Active' : 'Inactive' ?>
                        </span>
                    </p>
                </div>

                <div style="display: flex; flex-direction: column; gap: var(--spacing-sm);">
                    <?php if ($current_user['role'] === 'admin' && $user['id'] != $current_user['id']): ?>
                        <?php if (!$user['is_activated']): ?>
                            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/resend-activation') ?>" style="display: inline;">
                                <?= csrf_field() ?>
                                <button type="submit" class="btn" style="background: #17a2b8; color: white;">
                                    📧 Resend Activation
                                </button>
                            </form>
                        <?php endif; ?>

                        <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/toggle-status') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn" style="background: <?= $user['is_activated'] ? '#ffc107' : '#28a745' ?>; color: <?= $user['is_activated'] ? '#000' : '#fff' ?>;"
                                    onclick="return confirm('Are you sure you want to <?= $user['is_activated'] ? 'deactivate' : 'activate' ?> this user?')">
                                <?= $user['is_activated'] ? '⏸️ Deactivate' : '▶️ Activate' ?>
                            </button>
                        </form>

                        <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/reset-password') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn" style="background: #ffc107; color: #000;"
                                    onclick="return confirm('Are you sure you want to reset this user\'s password?')">
                                🔑 Reset Password
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Profile Details -->
        <div style="display: grid; gap: var(--spacing-xl);">
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">Basic Information</div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-md);">
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <span style="font-weight: 500; color: var(--text-secondary); font-size: 0.9rem;">Full Name</span>
                        <span style="color: var(--text-primary); font-size: 1rem;"><?= esc($user['name']) ?></span>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <span style="font-weight: 500; color: var(--text-secondary); font-size: 0.9rem;">Username</span>
                        <span style="color: var(--text-primary); font-size: 1rem;"><?= esc($user['username']) ?></span>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <span style="font-weight: 500; color: var(--text-secondary); font-size: 0.9rem;">Email Address</span>
                        <span style="color: var(--text-primary); font-size: 1rem;"><?= esc($user['email']) ?></span>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <span style="font-weight: 500; color: var(--text-secondary); font-size: 0.9rem;">User Code</span>
                        <span style="font-family: var(--font-mono); background: rgba(255, 255, 255, 0.1); padding: var(--spacing-xs); border-radius: var(--radius-sm); color: var(--text-primary); font-size: 1rem;"><?= esc($user['user_code']) ?></span>
                    </div>
                </div>
            </div>

            <!-- Account Activity -->
            <div class="card">
                <div class="card-header">Account Activity</div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-md);">
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(255, 255, 255, 0.05); border-radius: var(--radius-md);">
                        <div style="font-size: 2rem; flex-shrink: 0;">🕐</div>
                        <div>
                            <h3 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 1rem;">Last Login</h3>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;"><?= $user['last_login_at'] ? date('F j, Y \a\t g:i A', strtotime($user['last_login_at'])) : 'Never logged in' ?></p>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(255, 255, 255, 0.05); border-radius: var(--radius-md);">
                        <div style="font-size: 2rem; flex-shrink: 0;">📅</div>
                        <div>
                            <h3 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 1rem;">Account Created</h3>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;"><?= date('F j, Y \a\t g:i A', strtotime($user['created_at'])) ?></p>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(255, 255, 255, 0.05); border-radius: var(--radius-md);">
                        <div style="font-size: 2rem; flex-shrink: 0;">🔄</div>
                        <div>
                            <h3 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 1rem;">Last Updated</h3>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;"><?= $user['updated_at'] ? date('F j, Y \a\t g:i A', strtotime($user['updated_at'])) : 'Never updated' ?></p>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(255, 255, 255, 0.05); border-radius: var(--radius-md);">
                        <div style="font-size: 2rem; flex-shrink: 0;">✅</div>
                        <div>
                            <h3 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 1rem;">Activation Status</h3>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;"><?= $user['is_activated'] ? 'Account is activated and ready to use' : 'Account pending activation' ?></p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


<?= $this->endSection() ?>
